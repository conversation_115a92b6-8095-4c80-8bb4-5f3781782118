import { nextConfig } from "@dtbx/vitest-config/next";
import { defineConfig, mergeConfig } from 'vitest/config';

const customConfig = defineConfig({
  test: {
    setupFiles: ['./src/test-utils/setup.ts'],
    environment: 'jsdom',
    globals: true,
    include: ['src/**/*.{test,spec}.{ts,tsx}'],
    exclude: [
      'node_modules/**',
      'dist/**',
      '.next/**',
      'coverage/**',
    ],
    coverage: {
      provider: 'istanbul',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test-utils/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/.next/**',
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70,
        },
      },
    },
  },
});

export default mergeConfig(nextConfig, customConfig);
