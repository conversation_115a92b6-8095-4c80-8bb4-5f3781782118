import { describe, it, expect, vi } from 'vitest'
import { rootReducer } from '../store'

// Mock @dtbx/store/reducers
vi.mock('@dtbx/store/reducers', () => ({
  authReducer: (state = { isAuthenticated: false, token: null, decodedToken: null }, action: any) => {
    switch (action.type) {
      case 'auth/login':
        return { ...state, isAuthenticated: true, token: action.payload.token }
      case 'auth/logout':
        return { ...state, isAuthenticated: false, token: null, decodedToken: null }
      default:
        return state
    }
  },
  navigation: (state = { isSidebarCollapsed: false, documentToggle: false }, action: any) => {
    switch (action.type) {
      case 'navigation/setSidebarCollapsed':
        return { ...state, isSidebarCollapsed: action.payload }
      case 'navigation/setDocumentToggle':
        return { ...state, documentToggle: action.payload }
      default:
        return state
    }
  },
  notifications: (state = { localNotification: null, localNotificationType: 'info' }, action: any) => {
    switch (action.type) {
      case 'notifications/setNotification':
        return { 
          ...state, 
          localNotification: action.payload.message,
          localNotificationType: action.payload.type 
        }
      case 'notifications/clearNotification':
        return { ...state, localNotification: null }
      default:
        return state
    }
  },
  overlays: (state = {}, action: any) => {
    switch (action.type) {
      case 'overlays/showOverlay':
        return { ...state, [action.payload.id]: action.payload.data }
      case 'overlays/hideOverlay':
        const newState = { ...state }
        delete newState[action.payload.id]
        return newState
      default:
        return state
    }
  },
}))

describe('Root Reducer', () => {
  it('should combine all reducers correctly', () => {
    const initialState = rootReducer(undefined, { type: '@@INIT' })
    
    expect(initialState).toHaveProperty('auth')
    expect(initialState).toHaveProperty('navigation')
    expect(initialState).toHaveProperty('notifications')
    expect(initialState).toHaveProperty('overlays')
  })

  it('should have correct initial state structure', () => {
    const initialState = rootReducer(undefined, { type: '@@INIT' })
    
    // Check auth initial state
    expect(initialState.auth).toEqual({
      isAuthenticated: false,
      token: null,
      decodedToken: null,
    })
    
    // Check navigation initial state
    expect(initialState.navigation).toEqual({
      isSidebarCollapsed: false,
      documentToggle: false,
    })
    
    // Check notifications initial state
    expect(initialState.notifications).toEqual({
      localNotification: null,
      localNotificationType: 'info',
    })
    
    // Check overlays initial state
    expect(initialState.overlays).toEqual({})
  })

  it('should handle auth actions', () => {
    const initialState = rootReducer(undefined, { type: '@@INIT' })
    
    // Test login action
    const loginAction = {
      type: 'auth/login',
      payload: { token: 'test-token' }
    }
    const stateAfterLogin = rootReducer(initialState, loginAction)
    
    expect(stateAfterLogin.auth.isAuthenticated).toBe(true)
    expect(stateAfterLogin.auth.token).toBe('test-token')
    
    // Test logout action
    const logoutAction = { type: 'auth/logout' }
    const stateAfterLogout = rootReducer(stateAfterLogin, logoutAction)
    
    expect(stateAfterLogout.auth.isAuthenticated).toBe(false)
    expect(stateAfterLogout.auth.token).toBe(null)
  })

  it('should handle navigation actions', () => {
    const initialState = rootReducer(undefined, { type: '@@INIT' })
    
    // Test sidebar collapse action
    const collapseAction = {
      type: 'navigation/setSidebarCollapsed',
      payload: true
    }
    const stateAfterCollapse = rootReducer(initialState, collapseAction)
    
    expect(stateAfterCollapse.navigation.isSidebarCollapsed).toBe(true)
    
    // Test document toggle action
    const toggleAction = {
      type: 'navigation/setDocumentToggle',
      payload: true
    }
    const stateAfterToggle = rootReducer(stateAfterCollapse, toggleAction)
    
    expect(stateAfterToggle.navigation.documentToggle).toBe(true)
  })

  it('should handle notification actions', () => {
    const initialState = rootReducer(undefined, { type: '@@INIT' })
    
    // Test set notification action
    const setNotificationAction = {
      type: 'notifications/setNotification',
      payload: { message: 'Test notification', type: 'success' }
    }
    const stateWithNotification = rootReducer(initialState, setNotificationAction)
    
    expect(stateWithNotification.notifications.localNotification).toBe('Test notification')
    expect(stateWithNotification.notifications.localNotificationType).toBe('success')
    
    // Test clear notification action
    const clearNotificationAction = { type: 'notifications/clearNotification' }
    const stateAfterClear = rootReducer(stateWithNotification, clearNotificationAction)
    
    expect(stateAfterClear.notifications.localNotification).toBe(null)
  })

  it('should handle overlay actions', () => {
    const initialState = rootReducer(undefined, { type: '@@INIT' })
    
    // Test show overlay action
    const showOverlayAction = {
      type: 'overlays/showOverlay',
      payload: { id: 'modal1', data: { title: 'Test Modal' } }
    }
    const stateWithOverlay = rootReducer(initialState, showOverlayAction)
    
    expect(stateWithOverlay.overlays.modal1).toEqual({ title: 'Test Modal' })
    
    // Test hide overlay action
    const hideOverlayAction = {
      type: 'overlays/hideOverlay',
      payload: { id: 'modal1' }
    }
    const stateAfterHide = rootReducer(stateWithOverlay, hideOverlayAction)
    
    expect(stateAfterHide.overlays.modal1).toBeUndefined()
  })

  it('should not mutate state', () => {
    const initialState = rootReducer(undefined, { type: '@@INIT' })
    const action = {
      type: 'navigation/setSidebarCollapsed',
      payload: true
    }
    
    const newState = rootReducer(initialState, action)
    
    // Original state should not be mutated
    expect(initialState.navigation.isSidebarCollapsed).toBe(false)
    expect(newState.navigation.isSidebarCollapsed).toBe(true)
    expect(newState).not.toBe(initialState)
  })
})
