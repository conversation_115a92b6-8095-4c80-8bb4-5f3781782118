import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import AppProvider from "../AppProvider";

// Mock redux-persist
vi.mock("redux-persist", () => ({
  persistStore: vi.fn(),
}));

// Mock the store
vi.mock("../index", () => ({
  default: {
    getState: vi.fn(() => ({})),
    dispatch: vi.fn(),
    subscribe: vi.fn(),
    replaceReducer: vi.fn(),
  },
}));

describe("AppProvider", () => {
  it("renders children correctly", () => {
    render(
      <AppProvider>
        <div data-testid="test-child">Test Child Component</div>
      </AppProvider>,
    );

    expect(screen.getByTestId("test-child")).toBeInTheDocument();
    expect(screen.getByText("Test Child Component")).toBeInTheDocument();
  });

  it("provides Redux store to children", () => {
    const TestComponent = () => {
      // This component would normally use useSelector or useDispatch
      // For this test, we just verify it renders without errors
      return <div data-testid="redux-consumer">Redux Consumer</div>;
    };

    render(
      <AppProvider>
        <TestComponent />
      </AppProvider>,
    );

    expect(screen.getByTestId("redux-consumer")).toBeInTheDocument();
  });

  it("renders without crashing", () => {
    const { container } = render(
      <AppProvider>
        <div>Test Content</div>
      </AppProvider>,
    );

    expect(container).toBeInTheDocument();
    expect(screen.getByText("Test Content")).toBeInTheDocument();
  });

  it("wraps children with Provider component", () => {
    render(
      <AppProvider>
        <div data-testid="wrapped-content">Wrapped Content</div>
      </AppProvider>,
    );

    // The content should be rendered, indicating the Provider is working
    expect(screen.getByTestId("wrapped-content")).toBeInTheDocument();
  });

  it("handles multiple children", () => {
    render(
      <AppProvider>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
        <span data-testid="child-3">Child 3</span>
      </AppProvider>,
    );

    expect(screen.getByTestId("child-1")).toBeInTheDocument();
    expect(screen.getByTestId("child-2")).toBeInTheDocument();
    expect(screen.getByTestId("child-3")).toBeInTheDocument();
  });

  it("accepts React.ReactNode as children prop", () => {
    render(
      <AppProvider>
        <div>First child</div>
        <span>Second child</span>
        <div>String child</div>
        <div>123</div>
      </AppProvider>,
    );

    expect(screen.getByText("First child")).toBeInTheDocument();
    expect(screen.getByText("Second child")).toBeInTheDocument();
    expect(screen.getByText("String child")).toBeInTheDocument();
    expect(screen.getByText("123")).toBeInTheDocument();
  });
});
