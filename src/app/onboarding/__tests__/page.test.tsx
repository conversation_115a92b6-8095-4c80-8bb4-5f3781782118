import { describe, it, expect } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders } from '@/test-utils/test-utils'
import OnboardingPageRoute from '../page'

describe('Onboarding Page Route', () => {
  it('renders the OnboardingPage component', () => {
    renderWithProviders(<OnboardingPageRoute />)
    
    // Check that the OnboardingPage content is rendered
    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveTextContent('Boma Yangu Onboarding Page')
  })

  it('renders without crashing', () => {
    const { container } = renderWithProviders(<OnboardingPageRoute />)
    expect(container.firstChild).toBeInTheDocument()
  })

  it('displays all OnboardingPage content', () => {
    renderWithProviders(<OnboardingPageRoute />)
    
    // Verify that all the OnboardingPage content is present
    expect(screen.getByText('Boma Yangu Onboarding Page')).toBeInTheDocument()
    expect(screen.getByText(/This is a test of the BlissPro font/)).toBeInTheDocument()
    expect(screen.getByText('Font Test - Heading 6')).toBeInTheDocument()
    expect(screen.getByText('Font Test - Subtitle 1')).toBeInTheDocument()
    expect(screen.getByText('Font Test - Body 2 text')).toBeInTheDocument()
  })

  it('has proper component structure', () => {
    renderWithProviders(<OnboardingPageRoute />)
    
    // Check that the component has the expected structure
    const headings = screen.getAllByRole('heading')
    expect(headings).toHaveLength(2) // h1 and h6
  })
})
