import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import Page from "../page";

// Create a simple test theme
const testTheme = createTheme();

// Simple wrapper for Material-UI components
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={testTheme}>{children}</ThemeProvider>
);

describe("Home Page", () => {
  it("renders the HomePage component", () => {
    render(
      <TestWrapper>
        <Page />
      </TestWrapper>,
    );

    // Check that the HomePage content is rendered
    const heading = screen.getByRole("heading", { level: 1 });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent("Boma Yangu Home Page");
  });

  it("renders without crashing", () => {
    const { container } = render(
      <TestWrapper>
        <Page />
      </TestWrapper>,
    );
    expect(container.firstChild).toBeInTheDocument();
  });

  it("displays all HomePage content", () => {
    render(
      <TestWrapper>
        <Page />
      </TestWrapper>,
    );

    // Verify that all the HomePage content is present
    expect(screen.getByText("Boma Yangu Home Page")).toBeInTheDocument();
    expect(
      screen.getByText(/This is a test of the BlissPro font/),
    ).toBeInTheDocument();
    expect(screen.getByText("Font Test - Heading 6")).toBeInTheDocument();
    expect(screen.getByText("Font Test - Subtitle 1")).toBeInTheDocument();
    expect(screen.getByText("Font Test - Body 2 text")).toBeInTheDocument();
  });
});
