import { describe, it, expect } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders } from '@/test-utils/test-utils'
import Page from '../page'

describe('Home Page', () => {
  it('renders the HomePage component', () => {
    renderWithProviders(<Page />)
    
    // Check that the HomePage content is rendered
    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveTextContent('Boma Yangu Home Page')
  })

  it('renders without crashing', () => {
    const { container } = renderWithProviders(<Page />)
    expect(container.firstChild).toBeInTheDocument()
  })

  it('displays all HomePage content', () => {
    renderWithProviders(<Page />)
    
    // Verify that all the HomePage content is present
    expect(screen.getByText('Boma Yangu Home Page')).toBeInTheDocument()
    expect(screen.getByText(/This is a test of the BlissPro font/)).toBeInTheDocument()
    expect(screen.getByText('Font Test - Heading 6')).toBeInTheDocument()
    expect(screen.getByText('Font Test - Subtitle 1')).toBeInTheDocument()
    expect(screen.getByText('Font Test - Body 2 text')).toBeInTheDocument()
  })
})
