import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders, mockAuthState } from '@/test-utils/test-utils'
import { setupMocks, resetMocks, mockStoreHooks } from '@/test-utils/mocks'
import RootLayout from '../layout'

// Setup mocks before tests
setupMocks()

describe('RootLayout', () => {
  beforeEach(() => {
    resetMocks()
    // Setup default mock return values
    mockStoreHooks.useAppSelector.mockImplementation((selector) => {
      const state = mockAuthState
      return selector(state)
    })
    mockStoreHooks.useAppDispatch.mockReturnValue(vi.fn())
  })

  it('renders the basic HTML structure', () => {
    const { container } = renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    // Check for html and body elements
    expect(container.querySelector('html')).toBeInTheDocument()
    expect(container.querySelector('body')).toBeInTheDocument()
  })

  it('renders children content', () => {
    renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    expect(screen.getByTestId('test-children')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('includes all required providers', () => {
    renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    // Check for provider components
    expect(screen.getByTestId('emotion-cache-provider')).toBeInTheDocument()
    expect(screen.getByTestId('theme-config')).toBeInTheDocument()
    expect(screen.getByTestId('custom-scrollbar')).toBeInTheDocument()
    expect(screen.getByTestId('in-activity')).toBeInTheDocument()
    expect(screen.getByTestId('auth-wrapper')).toBeInTheDocument()
  })

  it('renders the dashboard layout components', () => {
    renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    // Check for dashboard components
    expect(screen.getByTestId('sidebar')).toBeInTheDocument()
    expect(screen.getByTestId('internal-nav-bar')).toBeInTheDocument()
    expect(screen.getByTestId('local-notification')).toBeInTheDocument()
  })

  it('passes correct props to sidebar', () => {
    renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    const sidebar = screen.getByTestId('sidebar')
    expect(sidebar).toHaveAttribute('data-bg-color', '#FFFFFF')
  })

  it('displays user profile in navigation bar', () => {
    renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    expect(screen.getByTestId('profile-name')).toHaveTextContent('Test User')
  })

  it('handles sidebar collapse functionality', () => {
    const mockDispatch = vi.fn()
    mockStoreHooks.useAppDispatch.mockReturnValue(mockDispatch)

    renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    const collapseButton = screen.getByText('Collapse')
    collapseButton.click()

    // The sidebar component should call the collapse function
    expect(collapseButton).toBeInTheDocument()
  })

  it('renders notification component with correct props', () => {
    renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    const notification = screen.getByTestId('local-notification')
    expect(notification).toHaveAttribute('data-type', 'info')
  })

  it('sets correct HTML lang attribute', () => {
    const { container } = renderWithProviders(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    const htmlElement = container.querySelector('html')
    expect(htmlElement).toHaveAttribute('lang', 'en')
  })
})
