import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'

// mocks for all external dependencies
const mockStore = configureStore({
  reducer: {
    auth: (state = { isAuthenticated: true, token: 'test-token', decodedToken: { name: 'Test User' } }) => state,
    navigation: (state = { isSidebarCollapsed: false, documentToggle: false }) => state,
    notifications: (state = { localNotification: null, localNotificationType: 'info' }) => state,
    overlays: (state = {}) => state,
  },
})

// Mock all @dtbx/ui/theme components (mui)
vi.mock('@dtbx/ui/theme', () => ({
  NextAppDirEmotionCacheProvider: ({ children }: any) => (
    <div data-testid="emotion-cache-provider">{children}</div>
  ),
  ThemeConfig: ({ children, themeType }: any) => (
    <div data-testid="theme-config" data-theme-type={themeType}>{children}</div>
  ),
}))

// Mock all @dtbx/ui/components (internal)
vi.mock('@dtbx/ui/components', () => ({
  AuthWrapper: ({ children, isLoggedIn }: any) => (
    <div data-testid="auth-wrapper" data-logged-in={String(isLoggedIn || true)}>{children}</div>
  ),
  CustomScrollbar: ({ children }: any) => (
    <div data-testid="custom-scrollbar">{children}</div>
  ),
  InActivity: ({ children, isLoggedIn }: any) => (
    <div data-testid="in-activity" data-logged-in={String(isLoggedIn || true)}>{children}</div>
  ),
  LocalNotification: ({ notification, notificationType, clearNotification }: any) => (
    <div data-testid="local-notification" data-type={notificationType}>
      {notification && <span data-testid="notification-message">{notification}</span>}
      <button data-testid="clear-notification" onClick={clearNotification}>Clear</button>
    </div>
  ),
}))

// Mock Sidebar component separately
vi.mock('@dtbx/ui/components/Sidebar', () => ({
  Sidebar: ({ sidebarConfig, sidebarCollapsed, bgColor, profile, refreshToken }: any) => (
    <div data-testid="sidebar" data-bg-color={bgColor} data-collapsed={String(sidebarCollapsed)}>
      <div data-testid="sidebar-profile">{profile?.name || 'No Profile'}</div>
      <button data-testid="refresh-token" onClick={refreshToken}>Refresh</button>
      {sidebarConfig?.map((item: any) => (
        <div key={item.id} data-testid={`sidebar-item-${item.id}`}>
          {item.title}
        </div>
      ))}
    </div>
  ),
}))

// Mock InternalNavBar component separately
vi.mock('@dtbx/ui/components/Appbar', () => ({
  InternalNavBar: ({ profile, refreshToken }: any) => (
    <div data-testid="internal-nav-bar">
      <span data-testid="nav-profile">{profile?.name || 'No Profile'}</span>
      <button data-testid="nav-refresh-token" onClick={refreshToken}>Refresh Token</button>
    </div>
  ),
}))

// Mock @dtbx/store hooks (create fresh mocks in the factory)
vi.mock('@dtbx/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))

vi.mock('@dtbx/store/actions', () => ({
  refreshToken: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  clearNotification: vi.fn(),
  setSidebarCollapsed: vi.fn(),
}))

vi.mock('@dtbx/store/utils', () => ({
  isLoggedIn: vi.fn(() => true),
}))

// Mock AppProvider
vi.mock('@/store/AppProvider', () => ({
  default: ({ children }: any) => (
    <Provider store={mockStore}>
      <div data-testid="app-provider">{children}</div>
    </Provider>
  ),
}))

// Mock sidebar config
vi.mock('./sidebar', () => ({
  sidebarConfig: [
    { id: '1', title: 'Home', path: '/home', module: 'Test' },
    { id: '2', title: 'Settings', path: '/settings', module: 'Test' },
  ],
}))

// Mock CSS import
vi.mock('./fonts.css', () => ({}))

import RootLayout from '../layout'

// Import the mocked modules to get access to the mock functions
import { useAppSelector, useAppDispatch } from '@dtbx/store'

describe('Layout Components - Component Isolation', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default mock implementations
    ;(useAppSelector as any).mockImplementation((selector: any) => {
      const state = {
        auth: { isAuthenticated: true, token: 'test-token', decodedToken: { name: 'Test User' } },
        navigation: { isSidebarCollapsed: false, documentToggle: false },
        notifications: { localNotification: null, localNotificationType: 'info' },
        overlays: {},
      }
      return selector(state)
    })

    ;(useAppDispatch as any).mockReturnValue(vi.fn())
  })

  describe('RootLayout Structure', () => {
    it('renders the basic HTML structure with correct attributes', () => {
      const { container } = render(
        <RootLayout>
          <div data-testid="test-content">Test Content</div>
        </RootLayout>
      )

      // Check that the component renders without crashing
      expect(container.firstChild).toBeInTheDocument()

      // Check that the test content is rendered
      expect(screen.getByTestId('test-content')).toBeInTheDocument()
    })

    it('renders all provider components in correct order', () => {
      render(
        <RootLayout>
          <div data-testid="test-content">Test Content</div>
        </RootLayout>
      )

      // Check provider hierarchy
      expect(screen.getByTestId('app-provider')).toBeInTheDocument()
      expect(screen.getByTestId('emotion-cache-provider')).toBeInTheDocument()
      expect(screen.getByTestId('theme-config')).toBeInTheDocument()
      expect(screen.getByTestId('custom-scrollbar')).toBeInTheDocument()
      expect(screen.getByTestId('in-activity')).toBeInTheDocument()
    })

    it('passes correct props to ThemeConfig', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      const themeConfig = screen.getByTestId('theme-config')
      expect(themeConfig).toHaveAttribute('data-theme-type', 'main')
    })

    it('renders children content', () => {
      render(
        <RootLayout>
          <div data-testid="test-content">Test Content</div>
        </RootLayout>
      )

      expect(screen.getByTestId('test-content')).toBeInTheDocument()
      expect(screen.getByText('Test Content')).toBeInTheDocument()
    })
  })

  describe('DashboardLayout Components', () => {
    it('renders sidebar with correct props', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      const sidebar = screen.getByTestId('sidebar')
      expect(sidebar).toBeInTheDocument()
      expect(sidebar).toHaveAttribute('data-bg-color', '#FFFFFF')
    })

    it('renders internal navigation bar', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      expect(screen.getByTestId('internal-nav-bar')).toBeInTheDocument()
    })

    it('renders local notification component', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      expect(screen.getByTestId('local-notification')).toBeInTheDocument()
    })

    it('displays user profile information in sidebar', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      // Check that profile elements exist (they may show fallback text)
      expect(screen.getByTestId('sidebar-profile')).toBeInTheDocument()
      expect(screen.getByTestId('nav-profile')).toBeInTheDocument()
    })
  })

  describe('Redux Integration', () => {
    it('uses Redux selectors correctly', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      // Verify selectors were called
      expect(useAppSelector).toHaveBeenCalled()
      expect(useAppDispatch).toHaveBeenCalled()
    })

    it('handles collapsed sidebar state', () => {
      ;(useAppSelector as any).mockImplementation((selector: any) => {
        const state = {
          auth: { isAuthenticated: true, decodedToken: { name: 'Test User' } },
          navigation: { isSidebarCollapsed: true, documentToggle: false },
          notifications: { localNotification: null, localNotificationType: 'info' },
          overlays: {},
        }
        return selector(state)
      })

      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      const sidebar = screen.getByTestId('sidebar')
      expect(sidebar).toHaveAttribute('data-collapsed')
    })

    it('displays notifications when present', () => {
      ;(useAppSelector as any).mockImplementation((selector: any) => {
        const state = {
          auth: { isAuthenticated: true, decodedToken: { name: 'Test User' } },
          navigation: { isSidebarCollapsed: false, documentToggle: false },
          notifications: { localNotification: 'Test notification', localNotificationType: 'success' },
          overlays: {},
        }
        return selector(state)
      })

      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      const notification = screen.getByTestId('local-notification')
      expect(notification).toHaveAttribute('data-type', 'success')
      expect(screen.getByTestId('notification-message')).toHaveTextContent('Test notification')
    })
  })

  describe('Authentication Integration', () => {
    it('passes authentication state to components', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      // Check that authentication components have the data-logged-in attribute
      expect(screen.getByTestId('auth-wrapper')).toHaveAttribute('data-logged-in')
      expect(screen.getByTestId('in-activity')).toHaveAttribute('data-logged-in')
    })

    it('handles authentication state properly', () => {
      render(
        <RootLayout>
          <div>Test</div>
        </RootLayout>
      )

      // Check that authentication components are rendered
      expect(screen.getByTestId('auth-wrapper')).toBeInTheDocument()
      expect(screen.getByTestId('in-activity')).toBeInTheDocument()
    })
  })
})