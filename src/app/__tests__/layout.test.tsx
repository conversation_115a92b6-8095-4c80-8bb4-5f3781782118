import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'

// Mock all external dependencies
vi.mock('@dtbx/ui/theme', () => ({
  NextAppDirEmotionCacheProvider: ({ children }: any) => children,
  ThemeConfig: ({ children }: any) => children,
}))

vi.mock('@dtbx/ui/components', () => ({
  AuthWrapper: ({ children }: any) => children,
  CustomScrollbar: ({ children }: any) => children,
  InActivity: ({ children }: any) => children,
  LocalNotification: () => null,
  Sidebar: () => null,
  InternalNavBar: () => null,
}))

vi.mock('@dtbx/store', () => ({
  useAppSelector: vi.fn(() => ({})),
  useAppDispatch: vi.fn(() => vi.fn()),
}))

vi.mock('@dtbx/store/actions', () => ({
  refreshToken: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  clearNotification: vi.fn(),
  setSidebarCollapsed: vi.fn(),
}))

vi.mock('@dtbx/store/utils', () => ({
  isLoggedIn: vi.fn(() => true),
}))

vi.mock('@/store/AppProvider', () => ({
  default: ({ children }: any) => children,
}))

vi.mock('./sidebar', () => ({
  sidebarConfig: [],
}))

import RootLayout from '../layout'

describe('RootLayout', () => {
  it('renders the basic HTML structure', () => {
    const { container } = render(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    // Check for html and body elements
    expect(container.querySelector('html')).toBeInTheDocument()
    expect(container.querySelector('body')).toBeInTheDocument()
  })

  it('renders children content', () => {
    render(
      <RootLayout>
        <div data-testid="test-children">Test Content</div>
      </RootLayout>
    )

    // The children should be rendered somewhere in the DOM
    expect(document.querySelector('[data-testid="test-children"]')).toBeInTheDocument()
  })

  it('renders without crashing', () => {
    const { container } = render(
      <RootLayout>
        <div>Test Content</div>
      </RootLayout>
    )

    expect(container).toBeInTheDocument()
  })

  it('sets correct HTML lang attribute', () => {
    const { container } = render(
      <RootLayout>
        <div>Test Content</div>
      </RootLayout>
    )

    const htmlElement = container.querySelector('html')
    expect(htmlElement).toHaveAttribute('lang', 'en')
  })

  it('includes all provider components in the structure', () => {
    const { container } = render(
      <RootLayout>
        <div>Test Content</div>
      </RootLayout>
    )

    // The layout should render without errors, indicating all providers are working
    expect(container.firstChild).toBeInTheDocument()
  })

  it('handles empty children', () => {
    const { container } = render(
      <RootLayout>
        {null}
      </RootLayout>
    )

    expect(container).toBeInTheDocument()
  })

  it('handles multiple children', () => {
    render(
      <RootLayout>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </RootLayout>
    )

    expect(document.querySelector('[data-testid="child-1"]')).toBeInTheDocument()
    expect(document.querySelector('[data-testid="child-2"]')).toBeInTheDocument()
  })
})