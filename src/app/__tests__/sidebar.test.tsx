import { describe, it, expect, vi } from "vitest";
import { sidebarConfig } from "../sidebar";

// Mock @dtbx/ui/icons
vi.mock("@dtbx/ui/icons", () => ({
  UserProfileIcon: () => <div data-testid="user-profile-icon">👤</div>,
  HomeIcon: () => <div data-testid="home-icon">🏠</div>,
}));

describe("Sidebar Configuration", () => {
  it("should export sidebarConfig as an array", () => {
    expect(Array.isArray(sidebarConfig)).toBe(true);
    expect(sidebarConfig.length).toBeGreaterThan(0);
  });

  it("should have correct number of sidebar items", () => {
    expect(sidebarConfig).toHaveLength(2);
  });

  it("should have home sidebar item with correct properties", () => {
    const homeItem = sidebarConfig.find((item) => item.id === "1");

    expect(homeItem).toBeDefined();
    expect(homeItem?.title).toBe("home");
    expect(homeItem?.path).toBe("/home");
    expect(homeItem?.module).toBe("Boma");
    expect(homeItem?.isProductionReady).toBe(true);
    expect(homeItem?.icon).toBeDefined();
  });

  it("should have onboarding sidebar item with correct properties", () => {
    const onboardingItem = sidebarConfig.find((item) => item.id === "2");

    expect(onboardingItem).toBeDefined();
    expect(onboardingItem?.title).toBe("Onboarding");
    expect(onboardingItem?.path).toBe("/onboarding");
    expect(onboardingItem?.module).toBe("Boma");
    expect(onboardingItem?.isProductionReady).toBe(true);
    expect(onboardingItem?.icon).toBeDefined();
  });

  it("should have unique IDs for all items", () => {
    const ids = sidebarConfig.map((item) => item.id);
    const uniqueIds = [...new Set(ids)];

    expect(ids.length).toBe(uniqueIds.length);
  });

  it("should have unique paths for all items", () => {
    const paths = sidebarConfig.map((item) => item.path);
    const uniquePaths = [...new Set(paths)];

    expect(paths.length).toBe(uniquePaths.length);
  });

  it("should have all items marked as production ready", () => {
    const allProductionReady = sidebarConfig.every(
      (item) => item.isProductionReady === true,
    );
    expect(allProductionReady).toBe(true);
  });

  it("should have all items belong to Boma module", () => {
    const allBomaModule = sidebarConfig.every((item) => item.module === "Boma");
    expect(allBomaModule).toBe(true);
  });

  it("should have valid path format for all items", () => {
    const validPaths = sidebarConfig.every(
      (item) => item.path.startsWith("/") && item.path.length > 1,
    );
    expect(validPaths).toBe(true);
  });

  it("should have non-empty titles for all items", () => {
    const validTitles = sidebarConfig.every(
      (item) => typeof item.title === "string" && item.title.length > 0,
    );
    expect(validTitles).toBe(true);
  });

  it("should have React elements as icons", () => {
    const validIcons = sidebarConfig.every(
      (item) => item.icon && typeof item.icon === "object",
    );
    expect(validIcons).toBe(true);
  });

  it("should match ISidebarConfigItem interface structure", () => {
    sidebarConfig.forEach((item) => {
      expect(item).toHaveProperty("id");
      expect(item).toHaveProperty("title");
      expect(item).toHaveProperty("path");
      expect(item).toHaveProperty("module");
      expect(item).toHaveProperty("icon");
      expect(item).toHaveProperty("isProductionReady");

      expect(typeof item.id).toBe("string");
      expect(typeof item.title).toBe("string");
      expect(typeof item.path).toBe("string");
      expect(typeof item.module).toBe("string");
      expect(typeof item.isProductionReady).toBe("boolean");
    });
  });

  it("should be ordered correctly (home first, then onboarding)", () => {
    expect(sidebarConfig[0].title).toBe("home");
    expect(sidebarConfig[1].title).toBe("Onboarding");
  });

  it("should have consistent casing in titles", () => {
    // Check that titles follow expected casing patterns
    expect(sidebarConfig[0].title).toBe("home"); // lowercase
    expect(sidebarConfig[1].title).toBe("Onboarding"); // title case
  });
});
