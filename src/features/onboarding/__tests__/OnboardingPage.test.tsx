import { describe, it, expect } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders } from '@/test-utils/test-utils'
import OnboardingPage from '../OnboardingPage'

describe('OnboardingPage', () => {
  it('renders the main heading', () => {
    renderWithProviders(<OnboardingPage />)
    
    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveTextContent('Boma Yangu Onboarding Page')
  })

  it('renders the font test description', () => {
    renderWithProviders(<OnboardingPage />)
    
    const description = screen.getByText(/This is a test of the BlissPro font/)
    expect(description).toBeInTheDocument()
  })

  it('renders all typography variants', () => {
    renderWithProviders(<OnboardingPage />)
    
    // Check for h1 heading
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Boma Yangu Onboarding Page')
    
    // Check for h6 heading
    expect(screen.getByRole('heading', { level: 6 })).toHaveTextContent('Font Test - Heading 6')
    
    // Check for body text
    expect(screen.getByText('Font Test - Subtitle 1')).toBeInTheDocument()
    expect(screen.getByText('Font Test - Body 2 text')).toBeInTheDocument()
  })

  it('has proper structure with Stack container and padding', () => {
    const { container } = renderWithProviders(<OnboardingPage />)
    
    // Check that the component renders without crashing
    expect(container.firstChild).toBeInTheDocument()
  })

  it('applies correct styling to elements', () => {
    renderWithProviders(<OnboardingPage />)
    
    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading).toHaveStyle({ marginBottom: '16px' }) // 2 * 8px (theme spacing)
  })

  it('renders all text content correctly', () => {
    renderWithProviders(<OnboardingPage />)
    
    // Verify all expected text is present
    expect(screen.getByText('Boma Yangu Onboarding Page')).toBeInTheDocument()
    expect(screen.getByText(/This is a test of the BlissPro font/)).toBeInTheDocument()
    expect(screen.getByText('Font Test - Heading 6')).toBeInTheDocument()
    expect(screen.getByText('Font Test - Subtitle 1')).toBeInTheDocument()
    expect(screen.getByText('Font Test - Body 2 text')).toBeInTheDocument()
  })

  it('has accessible heading structure', () => {
    renderWithProviders(<OnboardingPage />)
    
    const headings = screen.getAllByRole('heading')
    expect(headings).toHaveLength(2) // h1 and h6
    
    // Check heading levels
    expect(headings[0]).toHaveProperty('tagName', 'H1')
    expect(headings[1]).toHaveProperty('tagName', 'H6')
  })

  it('has proper container styling with padding', () => {
    renderWithProviders(<OnboardingPage />)
    
    // The Stack component should have padding applied
    const container = screen.getByText('Boma Yangu Onboarding Page').closest('div')
    expect(container).toBeInTheDocument()
  })

  it('displays font testing content in correct order', () => {
    renderWithProviders(<OnboardingPage />)
    
    const textElements = [
      'Boma Yangu Onboarding Page',
      /This is a test of the BlissPro font/,
      'Font Test - Heading 6',
      'Font Test - Subtitle 1',
      'Font Test - Body 2 text'
    ]
    
    textElements.forEach(text => {
      if (typeof text === 'string') {
        expect(screen.getByText(text)).toBeInTheDocument()
      } else {
        expect(screen.getByText(text)).toBeInTheDocument()
      }
    })
  })
})
