import { describe, it, expect } from "vitest";

describe("Basic Test Setup", () => {
  it("should run a basic test", () => {
    expect(1 + 1).toBe(2);
  });

  it("should handle string operations", () => {
    const str = "Hello World";
    expect(str.toLowerCase()).toBe("hello world");
    expect(str.length).toBe(11);
  });

  it("should handle array operations", () => {
    const arr = [1, 2, 3];
    expect(arr.length).toBe(3);
    expect(arr.includes(2)).toBe(true);
  });

  it("should handle object operations", () => {
    const obj = { name: "Test", value: 42 };
    expect(obj.name).toBe("Test");
    expect(obj.value).toBe(42);
  });
});
