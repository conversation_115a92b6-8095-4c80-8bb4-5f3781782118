import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  mockDtbxComponents,
  mockThemeComponents,
  mockIcons,
  mockStoreHooks,
  mockStoreActions,
  mockStoreUtils,
  setupMocks,
  resetMocks,
} from "../mocks";

describe("Mock Utilities", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("mockDtbxComponents", () => {
    it("should provide mock functions for all DTBX components", () => {
      expect(mockDtbxComponents.AuthWrapper).toBeDefined();
      expect(mockDtbxComponents.CustomScrollbar).toBeDefined();
      expect(mockDtbxComponents.InActivity).toBeDefined();
      expect(mockDtbxComponents.LocalNotification).toBeDefined();
      expect(mockDtbxComponents.Sidebar).toBeDefined();
      expect(mockDtbxComponents.InternalNavBar).toBeDefined();

      // Test that they are functions
      expect(typeof mockDtbxComponents.AuthWrapper).toBe("function");
      expect(typeof mockDtbxComponents.CustomScrollbar).toBe("function");
      expect(typeof mockDtbxComponents.InActivity).toBe("function");
      expect(typeof mockDtbxComponents.LocalNotification).toBe("function");
      expect(typeof mockDtbxComponents.Sidebar).toBe("function");
      expect(typeof mockDtbxComponents.InternalNavBar).toBe("function");
    });

    it("should return children for wrapper components", () => {
      const testChildren = "test children";

      expect(mockDtbxComponents.AuthWrapper({ children: testChildren })).toBe(
        testChildren,
      );
      expect(
        mockDtbxComponents.CustomScrollbar({ children: testChildren }),
      ).toBe(testChildren);
      expect(mockDtbxComponents.InActivity({ children: testChildren })).toBe(
        testChildren,
      );
    });

    it("should return null for non-wrapper components", () => {
      expect(mockDtbxComponents.LocalNotification()).toBe(null);
      expect(mockDtbxComponents.Sidebar()).toBe(null);
      expect(mockDtbxComponents.InternalNavBar()).toBe(null);
    });
  });

  describe("mockThemeComponents", () => {
    it("should provide mock functions for theme components", () => {
      expect(mockThemeComponents.NextAppDirEmotionCacheProvider).toBeDefined();
      expect(mockThemeComponents.ThemeConfig).toBeDefined();

      expect(typeof mockThemeComponents.NextAppDirEmotionCacheProvider).toBe(
        "function",
      );
      expect(typeof mockThemeComponents.ThemeConfig).toBe("function");
    });

    it("should return children for theme wrapper components", () => {
      const testChildren = "test children";

      expect(
        mockThemeComponents.NextAppDirEmotionCacheProvider({
          children: testChildren,
        }),
      ).toBe(testChildren);
      expect(mockThemeComponents.ThemeConfig({ children: testChildren })).toBe(
        testChildren,
      );
    });
  });

  describe("mockIcons", () => {
    it("should provide mock functions for icon components", () => {
      expect(mockIcons.HomeIcon).toBeDefined();
      expect(mockIcons.UserProfileIcon).toBeDefined();

      expect(typeof mockIcons.HomeIcon).toBe("function");
      expect(typeof mockIcons.UserProfileIcon).toBe("function");
    });

    it("should return null for icon components", () => {
      expect(mockIcons.HomeIcon()).toBe(null);
      expect(mockIcons.UserProfileIcon()).toBe(null);
    });
  });

  describe("mockStoreHooks", () => {
    it("should provide mock functions for store hooks", () => {
      expect(mockStoreHooks.useAppSelector).toBeDefined();
      expect(mockStoreHooks.useAppDispatch).toBeDefined();

      expect(vi.isMockFunction(mockStoreHooks.useAppSelector)).toBe(true);
      expect(vi.isMockFunction(mockStoreHooks.useAppDispatch)).toBe(true);
    });

    it("should return a function from useAppDispatch", () => {
      const dispatch = mockStoreHooks.useAppDispatch();
      expect(typeof dispatch).toBe("function");
    });
  });

  describe("mockStoreActions", () => {
    it("should provide mock functions for store actions", () => {
      expect(mockStoreActions.refreshToken).toBeDefined();
      expect(mockStoreActions.clearNotification).toBeDefined();
      expect(mockStoreActions.setSidebarCollapsed).toBeDefined();

      expect(vi.isMockFunction(mockStoreActions.refreshToken)).toBe(true);
      expect(vi.isMockFunction(mockStoreActions.clearNotification)).toBe(true);
      expect(vi.isMockFunction(mockStoreActions.setSidebarCollapsed)).toBe(
        true,
      );
    });
  });

  describe("mockStoreUtils", () => {
    it("should provide mock functions for store utilities", () => {
      expect(mockStoreUtils.isLoggedIn).toBeDefined();
      expect(vi.isMockFunction(mockStoreUtils.isLoggedIn)).toBe(true);
    });

    it("should return true by default for isLoggedIn", () => {
      expect(mockStoreUtils.isLoggedIn()).toBe(true);
    });
  });

  describe("setupMocks", () => {
    it("should be a function", () => {
      expect(typeof setupMocks).toBe("function");
    });

    it("should execute without throwing", () => {
      expect(() => setupMocks()).not.toThrow();
    });
  });

  describe("resetMocks", () => {
    it("should be a function", () => {
      expect(typeof resetMocks).toBe("function");
    });

    it("should execute without throwing", () => {
      expect(() => resetMocks()).not.toThrow();
    });

    it("should reset all mock functions", () => {
      // Call some mock functions first
      mockStoreHooks.useAppSelector();
      mockStoreActions.refreshToken();
      mockStoreUtils.isLoggedIn();

      // Reset mocks
      resetMocks();

      // Verify mocks were reset(smoke test)
      expect(() => resetMocks()).not.toThrow();
    });
  });

  describe("Mock Integration", () => {
    it("should work together as a complete mocking system", () => {
      // Test that all mock objects are properly structured
      expect(mockDtbxComponents).toBeTypeOf("object");
      expect(mockThemeComponents).toBeTypeOf("object");
      expect(mockIcons).toBeTypeOf("object");
      expect(mockStoreHooks).toBeTypeOf("object");
      expect(mockStoreActions).toBeTypeOf("object");
      expect(mockStoreUtils).toBeTypeOf("object");

      // Test that setup and reset functions exist
      expect(setupMocks).toBeTypeOf("function");
      expect(resetMocks).toBeTypeOf("function");
    });
  });
});
