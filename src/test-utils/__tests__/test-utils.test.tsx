import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders, createTestStore, mockAuthState, mockUser } from '../test-utils'
import { useSelector } from 'react-redux'

// Test component that uses Redux
const TestReduxComponent = () => {
  const auth = useSelector((state: any) => state.auth)
  const navigation = useSelector((state: any) => state.navigation)
  
  return (
    <div>
      <div data-testid="auth-status">
        {auth.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
      <div data-testid="sidebar-status">
        {navigation.isSidebarCollapsed ? 'Collapsed' : 'Expanded'}
      </div>
      <div data-testid="user-name">
        {auth.decodedToken?.name || 'No User'}
      </div>
    </div>
  )
}

describe('Test Utils', () => {
  describe('createTestStore', () => {
    it('should create a store with default state', () => {
      const store = createTestStore()
      const state = store.getState()
      
      expect(state).toHaveProperty('auth')
      expect(state).toHaveProperty('navigation')
      expect(state).toHaveProperty('notifications')
      expect(state).toHaveProperty('overlays')
    })

    it('should create a store with custom initial state', () => {
      const customState = {
        auth: { isAuthenticated: true, token: 'test-token' }
      }
      const store = createTestStore(customState)
      const state = store.getState()
      
      expect(state.auth.isAuthenticated).toBe(true)
      expect(state.auth.token).toBe('test-token')
    })

    it('should allow dispatching actions', () => {
      const store = createTestStore()
      const testAction = { type: 'TEST_ACTION', payload: 'test' }
      
      expect(() => {
        store.dispatch(testAction)
      }).not.toThrow()
    })
  })

  describe('renderWithProviders', () => {
    it('should render component with default providers', () => {
      renderWithProviders(<div data-testid="test-component">Test</div>)
      
      expect(screen.getByTestId('test-component')).toBeInTheDocument()
      expect(screen.getByText('Test')).toBeInTheDocument()
    })

    it('should provide Redux store to components', () => {
      renderWithProviders(<TestReduxComponent />, {
        initialState: mockAuthState
      })
      
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated')
      expect(screen.getByTestId('sidebar-status')).toHaveTextContent('Expanded')
      expect(screen.getByTestId('user-name')).toHaveTextContent('Test User')
    })

    it('should use custom store when provided', () => {
      const customStore = createTestStore({
        auth: { isAuthenticated: false, decodedToken: null },
        navigation: { isSidebarCollapsed: true }
      })
      
      renderWithProviders(<TestReduxComponent />, { store: customStore })
      
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated')
      expect(screen.getByTestId('sidebar-status')).toHaveTextContent('Collapsed')
      expect(screen.getByTestId('user-name')).toHaveTextContent('No User')
    })

    it('should return store instance', () => {
      const { store } = renderWithProviders(<div>Test</div>)
      
      expect(store).toBeDefined()
      expect(store.getState).toBeDefined()
      expect(store.dispatch).toBeDefined()
    })

    it('should apply Material-UI theme', () => {
      const { container } = renderWithProviders(<div>Test</div>)
      
      // The component should render without theme-related errors
      expect(container.firstChild).toBeInTheDocument()
    })
  })

  describe('Mock Data', () => {
    it('should provide mockUser with correct structure', () => {
      expect(mockUser).toHaveProperty('id')
      expect(mockUser).toHaveProperty('name')
      expect(mockUser).toHaveProperty('email')
      
      expect(mockUser.id).toBe('1')
      expect(mockUser.name).toBe('Test User')
      expect(mockUser.email).toBe('<EMAIL>')
    })

    it('should provide mockAuthState with correct structure', () => {
      expect(mockAuthState).toHaveProperty('auth')
      expect(mockAuthState).toHaveProperty('navigation')
      expect(mockAuthState).toHaveProperty('notifications')
      expect(mockAuthState).toHaveProperty('overlays')
      
      expect(mockAuthState.auth.isAuthenticated).toBe(true)
      expect(mockAuthState.auth.decodedToken).toEqual(mockUser)
      expect(mockAuthState.navigation.isSidebarCollapsed).toBe(false)
      expect(mockAuthState.notifications.localNotification).toBe(null)
    })

    it('should have consistent mock data', () => {
      expect(mockAuthState.auth.decodedToken).toEqual(mockUser)
    })
  })

  describe('Integration', () => {
    it('should work with complex component trees', () => {
      const ComplexComponent = () => (
        <div>
          <TestReduxComponent />
          <div data-testid="nested-content">Nested Content</div>
        </div>
      )
      
      renderWithProviders(<ComplexComponent />, {
        initialState: mockAuthState
      })
      
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated')
      expect(screen.getByTestId('nested-content')).toHaveTextContent('Nested Content')
    })

    it('should handle multiple renders without conflicts', () => {
      renderWithProviders(<div data-testid="first">First</div>)
      expect(screen.getByTestId('first')).toBeInTheDocument()
      
      renderWithProviders(<div data-testid="second">Second</div>)
      expect(screen.getByTestId('second')).toBeInTheDocument()
    })
  })
})
