import { describe, it, expect } from "vitest";
import { createTestStore, mockAuthState, mockUser } from "../test-utils";

describe("Test Utils", () => {
  describe("createTestStore", () => {
    it("should create a store with default state", () => {
      const store = createTestStore();
      const state = store.getState();

      expect(state).toHaveProperty("auth");
      expect(state).toHaveProperty("navigation");
      expect(state).toHaveProperty("notifications");
      expect(state).toHaveProperty("overlays");
    });

    it("should create a store with custom initial state", () => {
      const customState = {
        auth: { isAuthenticated: true, token: "test-token" },
      };
      const store = createTestStore(customState);
      const state = store.getState();

      expect((state.auth as any).isAuthenticated).toBe(true);
      expect((state.auth as any).token).toBe("test-token");
    });

    it("should allow dispatching actions", () => {
      const store = createTestStore();
      const testAction = { type: "TEST_ACTION", payload: "test" };

      expect(() => {
        store.dispatch(testAction);
      }).not.toThrow();
    });
  });

  describe("Mock Data", () => {
    it("should provide mockUser with correct structure", () => {
      expect(mockUser).toHaveProperty("id");
      expect(mockUser).toHaveProperty("name");
      expect(mockUser).toHaveProperty("email");

      expect(mockUser.id).toBe("1");
      expect(mockUser.name).toBe("Test User");
      expect(mockUser.email).toBe("<EMAIL>");
    });

    it("should provide mockAuthState with correct structure", () => {
      expect(mockAuthState).toHaveProperty("auth");
      expect(mockAuthState).toHaveProperty("navigation");
      expect(mockAuthState).toHaveProperty("notifications");
      expect(mockAuthState).toHaveProperty("overlays");

      expect(mockAuthState.auth.isAuthenticated).toBe(true);
      expect(mockAuthState.auth.decodedToken).toEqual(mockUser);
      expect(mockAuthState.navigation.isSidebarCollapsed).toBe(false);
      expect(mockAuthState.notifications.localNotification).toBe(null);
    });

    it("should have consistent mock data", () => {
      expect(mockAuthState.auth.decodedToken).toEqual(mockUser);
    });
  });

  describe("Mock Data", () => {
    it("should provide mockUser with correct structure", () => {
      expect(mockUser).toHaveProperty("id");
      expect(mockUser).toHaveProperty("name");
      expect(mockUser).toHaveProperty("email");

      expect(mockUser.id).toBe("1");
      expect(mockUser.name).toBe("Test User");
      expect(mockUser.email).toBe("<EMAIL>");
    });

    it("should provide mockAuthState with correct structure", () => {
      expect(mockAuthState).toHaveProperty("auth");
      expect(mockAuthState).toHaveProperty("navigation");
      expect(mockAuthState).toHaveProperty("notifications");
      expect(mockAuthState).toHaveProperty("overlays");

      expect(mockAuthState.auth.isAuthenticated).toBe(true);
      expect(mockAuthState.auth.decodedToken).toEqual(mockUser);
      expect(mockAuthState.navigation.isSidebarCollapsed).toBe(false);
      expect(mockAuthState.notifications.localNotification).toBe(null);
    });

    it("should have consistent mock data", () => {
      expect(mockAuthState.auth.decodedToken).toEqual(mockUser);
    });
  });
});
