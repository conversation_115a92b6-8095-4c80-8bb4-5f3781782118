import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { CssBaseline } from "@mui/material";
import { rootReducer } from "@/store/reducers/store";

// Create a test theme
const testTheme = createTheme({
  palette: {
    mode: "light",
  },
});

// Create a test store with initial state
export const createTestStore = (initialState?: any) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState: initialState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
};

// Test wrapper component that provides all necessary providers
interface TestWrapperProps {
  children: React.ReactNode;
  initialState?: any;
  store?: ReturnType<typeof createTestStore>;
}

const TestWrapper: React.FC<TestWrapperProps> = ({
  children,
  initialState,
  store: customStore,
}) => {
  const store = customStore || createTestStore(initialState);

  return (
    <Provider store={store}>
      <ThemeProvider theme={testTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </Provider>
  );
};

// Custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  initialState?: any;
  store?: ReturnType<typeof createTestStore>;
}

export const renderWithProviders = (
  ui: ReactElement,
  options: CustomRenderOptions = {},
) => {
  const { initialState, store, ...renderOptions } = options;

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper initialState={initialState} store={store}>
      {children}
    </TestWrapper>
  );

  return {
    store: store || createTestStore(initialState),
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Mock data generators
export const mockUser = {
  id: "1",
  name: "Test User",
  email: "<EMAIL>",
};

export const mockAuthState = {
  auth: {
    isAuthenticated: true,
    decodedToken: mockUser,
    token: "mock-token",
  },
  navigation: {
    isSidebarCollapsed: false,
    documentToggle: false,
  },
  notifications: {
    localNotification: null,
    localNotificationType: "info",
  },
  overlays: {},
};

// Re-export everything from testing-library
export * from "@testing-library/react";
export { default as userEvent } from "@testing-library/user-event";
