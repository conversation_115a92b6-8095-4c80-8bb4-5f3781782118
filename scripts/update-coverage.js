#!/usr/bin/env node

/**
 * Script to automatically update coverage numbers in documentation
 * Usage: node scripts/update-coverage.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Extract coverage percentage from text output
 * @param {string} output - The coverage output text
 * @param {string} metric - The metric to extract (Statements, Functions, Lines, Branches)
 * @returns {number} - The coverage percentage
 */
function extractCoveragePercent(output, metric) {
  // Look for pattern like "Statements   : 93.1% ( 27/29 )"
  const regex = new RegExp(`${metric}\\s*:\\s*(\\d+(?:\\.\\d+)?)%`, 'i');
  const match = output.match(regex);
  return match ? parseFloat(match[1]) : 0;
}

/**
 * Get git user information
 * @returns {Object} - Git user name and email
 */
function getGitUserInfo() {
  try {
    const name = execSync('git config user.name', { encoding: 'utf8' }).trim();
    const email = execSync('git config user.email', { encoding: 'utf8' }).trim();
    return {
      name: name || 'Developer',
      email: email || 'developer@local'
    };
  } catch (error) {
    // Fallback to system user or environment variables
    const systemUser = process.env.USER || process.env.USERNAME || 'Developer';
    return {
      name: systemUser,
      email: `${systemUser.toLowerCase()}@local`
    };
  }
}

/**
 * Get current git branch
 * @returns {string} - Current branch name
 */
function getCurrentBranch() {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

/**
 * Get last commit hash (short)
 * @returns {string} - Short commit hash
 */
function getLastCommitHash() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

/**
 * Count tests in all test files
 * @returns {Object} - Test counts by file and total
 */
function countTests() {
  try {
    const glob = require('glob');
    const testFiles = glob.sync('src/**/*.{test,spec}.{ts,tsx}');

    let totalTests = 0;
    let totalFiles = 0;

    testFiles.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');

        // Count it and test blocks (actual test cases)
        const itMatches = content.match(/\b(it|test)\s*\(/g) || [];

        const fileTestCount = itMatches.length;

        if (fileTestCount > 0) {
          totalTests += fileTestCount;
          totalFiles++;
        }
      } catch (error) {
        console.warn(`Warning: Could not read test file ${file}`);
      }
    });

    return {
      total: totalTests,
      files: totalFiles
    };
  } catch (error) {
    console.warn('Warning: Could not count tests, using fallback');
    return { total: 119, files: 14 }; // Fallback to known values
  }
}

async function updateCoverageInDocs() {
  try {
    console.log('🧪 Running tests to get latest coverage...');

    // Run tests with coverage and capture output
    const coverageOutput = execSync('pnpm test:coverage', {
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // Extract coverage percentages from the text output
    const statements = extractCoveragePercent(coverageOutput, 'Statements');
    const functions = extractCoveragePercent(coverageOutput, 'Functions');
    const lines = extractCoveragePercent(coverageOutput, 'Lines');
    const branches = extractCoveragePercent(coverageOutput, 'Branches');
    
    // Get git information and timestamp
    const gitUser = getGitUserInfo();
    const currentBranch = getCurrentBranch();
    const lastCommit = getLastCommitHash();
    const currentDate = new Date().toISOString().split('T')[0];
    const currentTime = new Date().toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });

    // Count tests
    const testCounts = countTests();

    console.log('📊 Current Coverage:');
    console.log(`  Statements: ${statements}%`);
    console.log(`  Functions: ${functions}%`);
    console.log(`  Lines: ${lines}%`);
    console.log(`  Branches: ${branches}%`);
    console.log(`🧪 Test Summary:`);
    console.log(`  Total Tests: ${testCounts.total}`);
    console.log(`  Test Files: ${testCounts.files}`);
    console.log(`👤 Updated by: ${gitUser.name} (${gitUser.email})`);
    console.log(`🌿 Branch: ${currentBranch} (${lastCommit})`);
    
    // Read TESTING.md
    const testingMdPath = path.join(__dirname, '../docs/TESTING.md');
    let content = fs.readFileSync(testingMdPath, 'utf8');

    // Update document header information
    const documentInfoSection = `## Document Information

**Author**: Leroy Ombiji
**Created**: 2025-07-19
**Last Updated**: ${currentDate} at ${currentTime} by **${gitUser.name}**
**Maintainers**: DTB Frontend Team`;

    // Replace the document information section
    content = content.replace(
      /## Document Information[\s\S]*?(?=\n## |$)/,
      documentInfoSection + '\n\n'
    );
    
    // Update the coverage section
    const coverageSection = `## Coverage Requirements

We maintain high code coverage standards with minimum thresholds:

- **Statements**: 70%+ (Currently: ${statements}% ${statements >= 70 ? '✅' : '❌'})
- **Functions**: 70%+ (Currently: ${functions}% ${functions >= 70 ? '✅' : '❌'})
- **Lines**: 70%+ (Currently: ${lines}% ${lines >= 70 ? '✅' : '❌'})
- **Branches**: 70%+ (Currently: ${branches}% ${branches >= 70 ? '✅' : '❌'})

> 💡 **Tip**: Run \`pnpm test:coverage\` to see current coverage numbers
> 🤖 **Auto-updated**: ${currentDate} at ${currentTime} by **${gitUser.name}** (${gitUser.email})
> 🌿 **Branch**: ${currentBranch} | **Commit**: ${lastCommit}`;

    // Update test count in test structure section
    content = content.replace(
      /Total: \d+ tests across \d+ test files/,
      `Total: ${testCounts.total} tests across ${testCounts.files} test files`
    );

    // Replace the coverage section
    content = content.replace(
      /## Coverage Requirements[\s\S]*?(?=\n## |$)/,
      coverageSection + '\n\n'
    );
    
    // Write back to file
    fs.writeFileSync(testingMdPath, content);
    
    console.log('✅ Updated TESTING.md with latest coverage numbers');
    
  } catch (error) {
    console.error('❌ Error updating coverage:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  updateCoverageInDocs();
}

module.exports = { updateCoverageInDocs };
