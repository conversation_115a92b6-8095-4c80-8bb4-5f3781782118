#!/usr/bin/env node

/**
 * <PERSON>ript to automatically update coverage numbers in documentation
 * Usage: node scripts/update-coverage.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Extract coverage percentage from text output
 * @param {string} output - The coverage output text
 * @param {string} metric - The metric to extract (Statements, Functions, Lines, Branches)
 * @returns {number} - The coverage percentage
 */
function extractCoveragePercent(output, metric) {
  // Look for pattern like "Statements   : 93.1% ( 27/29 )"
  const regex = new RegExp(`${metric}\\s*:\\s*(\\d+(?:\\.\\d+)?)%`, 'i');
  const match = output.match(regex);
  return match ? parseFloat(match[1]) : 0;
}

async function updateCoverageInDocs() {
  try {
    console.log('🧪 Running tests to get latest coverage...');

    // Run tests with coverage and capture output
    const coverageOutput = execSync('pnpm test:coverage', {
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // Extract coverage percentages from the text output
    const statements = extractCoveragePercent(coverageOutput, 'Statements');
    const functions = extractCoveragePercent(coverageOutput, 'Functions');
    const lines = extractCoveragePercent(coverageOutput, 'Lines');
    const branches = extractCoveragePercent(coverageOutput, 'Branches');
    
    console.log('📊 Current Coverage:');
    console.log(`  Statements: ${statements}%`);
    console.log(`  Functions: ${functions}%`);
    console.log(`  Lines: ${lines}%`);
    console.log(`  Branches: ${branches}%`);
    
    // Read TESTING.md
    const testingMdPath = path.join(__dirname, '../docs/TESTING.md');
    let content = fs.readFileSync(testingMdPath, 'utf8');
    
    // Update the coverage section
    const coverageSection = `## Coverage Requirements

We maintain high code coverage standards with minimum thresholds:

- **Statements**: 70%+ (Currently: ${statements}% ${statements >= 70 ? '✅' : '❌'})
- **Functions**: 70%+ (Currently: ${functions}% ${functions >= 70 ? '✅' : '❌'})
- **Lines**: 70%+ (Currently: ${lines}% ${lines >= 70 ? '✅' : '❌'})
- **Branches**: 70%+ (Currently: ${branches}% ${branches >= 70 ? '✅' : '❌'})

> 💡 **Tip**: Run \`pnpm test:coverage\` to see current coverage numbers
> 🤖 **Auto-updated**: ${new Date().toISOString().split('T')[0]}`;

    // Replace the coverage section
    content = content.replace(
      /## Coverage Requirements[\s\S]*?(?=\n## |$)/,
      coverageSection + '\n\n'
    );
    
    // Write back to file
    fs.writeFileSync(testingMdPath, content);
    
    console.log('✅ Updated TESTING.md with latest coverage numbers');
    
  } catch (error) {
    console.error('❌ Error updating coverage:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  updateCoverageInDocs();
}

module.exports = { updateCoverageInDocs };
