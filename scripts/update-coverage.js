#!/usr/bin/env node

/**
 * Script to automatically update coverage numbers in documentation
 * Usage: node scripts/update-coverage.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

async function updateCoverageInDocs() {
  try {
    console.log('🧪 Running tests to get latest coverage...');
    
    // Run tests with coverage and capture JSON output
    const coverageOutput = execSync('pnpm test:coverage --reporter=json', { 
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // Parse the coverage data
    const coverageData = JSON.parse(coverageOutput);
    const summary = coverageData.coverageMap?.getCoverageSummary?.() || {};
    
    const statements = summary.statements?.pct || 0;
    const functions = summary.functions?.pct || 0;
    const lines = summary.lines?.pct || 0;
    const branches = summary.branches?.pct || 0;
    
    console.log('📊 Current Coverage:');
    console.log(`  Statements: ${statements}%`);
    console.log(`  Functions: ${functions}%`);
    console.log(`  Lines: ${lines}%`);
    console.log(`  Branches: ${branches}%`);
    
    // Read TESTING.md
    const testingMdPath = path.join(__dirname, '../docs/TESTING.md');
    let content = fs.readFileSync(testingMdPath, 'utf8');
    
    // Update the coverage section
    const coverageSection = `## Coverage Requirements

We maintain high code coverage standards with minimum thresholds:

- **Statements**: 70%+ (Currently: ${statements}% ${statements >= 70 ? '✅' : '❌'})
- **Functions**: 70%+ (Currently: ${functions}% ${functions >= 70 ? '✅' : '❌'})
- **Lines**: 70%+ (Currently: ${lines}% ${lines >= 70 ? '✅' : '❌'})
- **Branches**: 70%+ (Currently: ${branches}% ${branches >= 70 ? '✅' : '❌'})

> 💡 **Tip**: Run \`pnpm test:coverage\` to see current coverage numbers
> 🤖 **Auto-updated**: ${new Date().toISOString().split('T')[0]}`;

    // Replace the coverage section
    content = content.replace(
      /## Coverage Requirements[\s\S]*?(?=\n## |$)/,
      coverageSection + '\n\n'
    );
    
    // Write back to file
    fs.writeFileSync(testingMdPath, content);
    
    console.log('✅ Updated TESTING.md with latest coverage numbers');
    
  } catch (error) {
    console.error('❌ Error updating coverage:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  updateCoverageInDocs();
}

module.exports = { updateCoverageInDocs };
