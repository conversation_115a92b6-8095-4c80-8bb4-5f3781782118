name: Update Coverage Documentation

on:
  push:
    branches: [main, develop]
    paths:
      - 'src/**/*.ts'
      - 'src/**/*.tsx'
      - 'src/**/*.test.ts'
      - 'src/**/*.test.tsx'
  pull_request:
    branches: [main, develop]

jobs:
  update-coverage:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
          
      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Run tests and generate coverage
        run: pnpm test:coverage
        
      - name: Update coverage in documentation
        run: pnpm test:update-docs
        
      - name: Check for changes
        id: verify-changed-files
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Commit updated coverage
        if: steps.verify-changed-files.outputs.changed == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add docs/TESTING.md
          git commit -m "📊 Auto-update coverage documentation [skip ci]"
          git push
