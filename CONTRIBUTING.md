# Contributing to <PERSON><PERSON> Client

Thank you for your interest in contributing to Bo<PERSON> Yangu! This document provides guidelines and information for contributors.

## 🚀 Quick Start for Contributors

### Prerequisites

- Node.js 18.0+
- pnpm 8.0+
- Git latest version
- Basic knowledge of React, TypeScript, and Next.js

### Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/boma-yangu-client.git
   cd boma-yangu-client
   ```

2. **Install Dependencies**
   ```bash
   pnpm install
   ```

3. **Set up Environment**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your values
   ```

4. **Verify Setup**
   ```bash
   pnpm dev          # Start development server
   pnpm test         # Run tests
   pnpm lint         # Check code quality
   ```

## 📝 Development Process

### 1. Create Feature Branch

```bash
# Always branch from develop
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/your-feature-name
```

### 2. Development Guidelines

#### Code Style
- **TypeScript**: Use strict typing, avoid `any`
- **Components**: Functional components with hooks
- **Naming**: Use descriptive, camelCase names
- **Files**: Use PascalCase for components, camelCase for utilities

#### Testing Requirements
- **Coverage**: ≥70% for all changed files
- **Unit Tests**: Test component behavior and logic
- **Integration Tests**: Test component interactions
- **Test Files**: Place tests in `__tests__` directories

#### Example Test Structure
```typescript
// src/components/Button/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../Button'

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByText('Click me'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### 3. Quality Checks

Before committing, ensure:

```bash
# All tests pass
pnpm test

# Coverage meets requirements
pnpm test:check-changed

# No TypeScript errors
pnpm type-check

# No linting errors
pnpm lint

# Build succeeds
pnpm build
```

### 4. Commit Guidelines

Follow [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Format
<type>[optional scope]: <description>

# Examples
feat(auth): add OAuth2 login integration
fix(sidebar): resolve navigation menu collapse issue
docs: update API documentation
test(components): add unit tests for HomePage
refactor(utils): optimize date formatting functions
```

#### Commit Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding/updating tests
- `chore`: Maintenance tasks
- `perf`: Performance improvements

## 🔍 Code Review Process

### Pull Request Guidelines

1. **Clear Description**
   - What changes were made and why
   - How to test the changes
   - Screenshots for UI changes
   - Breaking changes (if any)

2. **Checklist**
   - [ ] Tests added/updated
   - [ ] Documentation updated
   - [ ] No console errors
   - [ ] Responsive design tested
   - [ ] Accessibility considered

3. **Template**
   ```markdown
   ## Description
   Brief description of changes

   ## Type of Change
   - [ ] Bug fix
   - [ ] New feature
   - [ ] Breaking change
   - [ ] Documentation update

   ## Testing
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] Manual testing completed

   ## Screenshots (if applicable)
   [Add screenshots here]
   ```

### Review Process

1. **Automated Checks**: CI/CD runs tests and quality checks
2. **Code Review**: Team member reviews for:
   - Code quality and best practices
   - Logic and functionality
   - Performance implications
   - Security considerations
3. **Testing**: Reviewer tests functionality
4. **Approval**: Changes approved and merged

## 🐛 Bug Reports

### Before Reporting

1. Check existing issues
2. Reproduce the bug
3. Test in different browsers/devices
4. Gather relevant information

### Bug Report Template

```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- OS: [e.g., macOS 12.0]
- Browser: [e.g., Chrome 96.0]
- Device: [e.g., iPhone 13]

**Screenshots**
If applicable, add screenshots
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Feature Description**
Clear description of the feature

**Problem Statement**
What problem does this solve?

**Proposed Solution**
How should this work?

**Alternatives Considered**
Other solutions you've considered

**Additional Context**
Any other relevant information
```

## 🏗️ Architecture Guidelines

### Project Structure

```
src/
├── app/                    # Next.js app directory
├── components/            # Reusable UI components
├── features/             # Feature-specific components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── store/                # State management
├── styles/               # Global styles
├── types/                # TypeScript type definitions
└── utils/                # Utility functions
```

### Component Guidelines

```typescript
// Good: Functional component with proper typing
interface ButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  onClick?: () => void
  disabled?: boolean
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  onClick,
  disabled = false
}) => {
  return (
    <button
      className={`btn btn-${variant}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  )
}
```

## 📚 Resources

### Documentation
- [React Documentation](https://react.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Material-UI Documentation](https://mui.com/)

### Tools
- [Vitest Testing Framework](https://vitest.dev/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [ESLint Rules](https://eslint.org/docs/rules/)
- [Prettier Configuration](https://prettier.io/docs/en/configuration.html)

## 🤝 Community

### Getting Help

- **GitHub Issues**: For bugs and feature requests
- **Team Slack**: For quick questions and discussions
- **Email**: <EMAIL> for urgent matters

### Code of Conduct

- Be respectful and inclusive
- Provide constructive feedback
- Help others learn and grow
- Follow project guidelines

---

Thank you for contributing to Boma Yangu! 🎉
