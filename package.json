{"name": "boma-yangu-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3010", "build": "next build", "start": "next start --port 3010", "lint": "next lint", "lint:fix": "next lint --fix", "clean": "rimraf .turbo .next coverage", "test": "vitest run", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:coverage:watch": "vitest --watch --coverage", "test:coverage:ui": "vitest --ui --coverage", "test:view-report": "vitest run --coverage && open coverage/index.html", "test:unit": "vitest run --exclude=src/test-utils/**/*.test.{ts,tsx}", "test:utils": "vitest run src/test-utils/**/*.test.{ts,tsx}", "test:ci": "vitest run --coverage --reporter=verbose --reporter=junit --outputFile=test-results.xml", "test:coverage:features": "vitest run src/features/ --coverage", "test:coverage:app": "vitest run src/app/ --coverage", "test:coverage:store": "vitest run src/store/ --coverage", "test:coverage:layout": "vitest run src/app/__tests__/layout.test.tsx --coverage", "test:update-docs": "node scripts/update-coverage.js", "type-check": "tsc --noEmit", "prepare": "husky"}, "lint-staged": {"**/*.{ts,tsx,md}": ["prettier --write --ignore-unknown"], "src/**/*.{test,spec}.{ts,tsx}": ["pnpm test:update-docs"]}, "dependencies": {"@dtbx/store": "^0.0.1", "@dtbx/ui": "^0.0.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-date-pickers": "^7.23.1", "@reduxjs/toolkit": "^2.8.2", "dayjs": "^1.11.13", "formik": "^2.4.6", "mui-tel-input": "^9.0.1", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tiny-case": "^1.0.3", "yup": "^1.6.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@dtbx/eslint-config": "^0.0.1", "@dtbx/typescript-config": "^0.0.1", "@dtbx/vitest-config": "^0.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "22.15.29", "@types/react": "19.1.6", "@vitejs/plugin-react": "^4.5.2", "@vitest/coverage-istanbul": "3.2.1", "@vitest/ui": "3.2.4", "branch-name-lint": "^3.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9"}}