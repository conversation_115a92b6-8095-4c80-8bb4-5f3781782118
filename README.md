# Boma Yangu Client

A modern home management platform built with Next.js, TypeScript, and Material-UI.

![Coverage](https://img.shields.io/badge/Coverage-93.1%25-brightgreen?style=flat-square)
![Tests](https://img.shields.io/badge/Tests-119%20passing-brightgreen?style=flat-square)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue?style=flat-square)
![Next.js](https://img.shields.io/badge/Next.js-15.3+-black?style=flat-square)

## 📋 Table of Contents

- [🚀 Quick Start](#-quick-start)
- [🛠️ Development Setup](#️-development-setup)
- [🧪 Testing](#-testing)
- [📝 Development Workflow](#-development-workflow)
- [🌿 Branch Strategy](#-branch-strategy)
- [💬 Commit Guidelines](#-commit-guidelines)
- [🔍 Code Quality](#-code-quality)
- [📚 Documentation](#-documentation)
- [🚀 Deployment](#-deployment)

## 🚀 Quick Start

### Prerequisites

- **Node.js**: 18.0+
- **pnpm**: 8.0+ (recommended package manager)
- **Git**: Latest version

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd boma-yangu-client

# Install dependencies
pnpm install

# Set up git hooks (for code quality enforcement)
pnpm prepare

# Start development server
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🛠️ Development Setup

### Environment Setup

1. **Copy environment variables**:
   ```bash
   cp .env.example .env.local
   ```

2. **Configure your environment**:
   ```env
   # Add your environment variables here
   NEXT_PUBLIC_API_URL=your_api_url
   ```

### Available Scripts

```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint
pnpm type-check       # TypeScript type checking

# Testing
pnpm test             # Run all tests
pnpm test:watch       # Run tests in watch mode
pnpm test:coverage    # Run tests with coverage
pnpm test:ui          # Open Vitest UI

# Coverage & Quality
pnpm test:check-changed    # Check coverage for changed files
pnpm test:enforce-coverage # Enforce coverage + update docs
pnpm test:update-docs      # Update test documentation
```

## 🧪 Testing

We maintain **93.1% code coverage** with **119 tests** across **14 test files**.

### Testing Stack

- **Vitest**: Fast unit test runner with native TypeScript support
- **React Testing Library**: Component testing utilities
- **jsdom**: Browser environment simulation
- **Istanbul**: Code coverage reporting

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run specific test suites
pnpm test:unit        # Unit tests only
pnpm test:utils       # Test utilities only
pnpm test:coverage:features  # Features coverage only
pnpm test:coverage:app       # App components coverage only

# View coverage report in browser
pnpm test:view-report
```

### Coverage Requirements

- **Minimum Coverage**: 70% for all changed files
- **Current Coverage**: 93.1% statements, 86.66% functions, 92.59% lines
- **Enforcement**: Pre-commit hooks block commits with insufficient coverage

For detailed testing information, see [docs/TESTING.md](docs/TESTING.md).

## 📝 Development Workflow

### 1. Create Feature Branch

```bash
# Create and switch to feature branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/bug-description

# Or for hotfixes
git checkout -b hotfix/critical-issue
```

### 2. Development Process

```bash
# Make your changes
# Write tests for new functionality
# Ensure tests pass
pnpm test

# Check coverage for your changes
pnpm test:check-changed

# Run type checking
pnpm type-check

# Run linting
pnpm lint
```

### 3. Pre-commit Quality Checks

Our pre-commit hooks automatically:
- ✅ **Format code** with Prettier
- ✅ **Check coverage** for changed files (≥70% required)
- ✅ **Update documentation** with latest test counts
- ✅ **Block commits** that don't meet quality standards

### 4. Commit and Push

```bash
# Stage your changes
git add .

# Commit (pre-commit hooks run automatically)
git commit -m "feat: add user authentication feature"

# Push to remote
git push origin feature/your-feature-name
```

## 🌿 Branch Strategy

We follow **Git Flow** with the following branch types:

### Main Branches
- **`main`**: Production-ready code
- **`develop`**: Integration branch for features

### Supporting Branches
- **`feature/*`**: New features and enhancements
- **`fix/*`**: Bug fixes
- **`hotfix/*`**: Critical production fixes
- **`release/*`**: Release preparation

### Branch Naming Convention

```bash
# Features
feature/user-authentication
feature/dashboard-redesign

# Bug fixes
fix/login-validation-error
fix/memory-leak-in-sidebar
fix/responsive-layout-issues

# Hotfixes
hotfix/security-vulnerability
hotfix/critical-payment-bug

# Releases
release/v1.2.0
release/v2.0.0-beta
```

## 💬 Commit Guidelines

We follow **Conventional Commits** specification for clear, semantic commit messages.

### Commit Message Format

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit Types

- **`feat`**: New feature
- **`fix`**: Bug fix
- **`docs`**: Documentation changes
- **`style`**: Code style changes (formatting, etc.)
- **`refactor`**: Code refactoring
- **`test`**: Adding or updating tests
- **`chore`**: Maintenance tasks
- **`perf`**: Performance improvements
- **`ci`**: CI/CD changes

### Examples

```bash
# Feature
git commit -m "feat(auth): add OAuth2 login integration"

# Bug fix
git commit -m "fix(sidebar): resolve navigation menu collapse issue"

# Documentation
git commit -m "docs: update Test guide documentation for repo"

# Testing
git commit -m "test(components): add unit tests for HomePage component"

# Breaking change
git commit -m "feat!: migrate to new authentication system

BREAKING CHANGE: The old auth tokens are no longer supported"
```

### Commit Message Rules

- ✅ Use present tense ("add feature" not "added feature")
- ✅ Use imperative mood ("move cursor to..." not "moves cursor to...")
- ✅ Limit first line to 72 characters
- ✅ Reference issues and pull requests when applicable
- ✅ Include breaking change information when needed

## 🔍 Code Quality

We maintain high code quality through automated tools and processes.

### Quality Standards

- **Code Coverage**: ≥70% for all changed files
- **TypeScript**: Strict mode enabled, no `any` types
- **ESLint**: Enforced coding standards
- **Prettier**: Consistent code formatting
- **Testing**: Comprehensive unit and integration tests

### Pre-commit Hooks

Automatically run on every commit via **Husky** and **lint-staged**:

```bash
# What happens when you commit:
git commit -m "feat: add new feature"

# 1. Prettier formats your code
# 2. ESLint checks for code quality issues
# 3. TypeScript validates types
# 4. Coverage check runs for changed files
# 5. Documentation updates automatically
# 6. Commit proceeds only if all checks pass
```

### Pre-push Hooks

Additional checks before pushing to remote:

```bash
# What happens when you push:
git push origin feature/my-feature

# 1. Full test suite runs
# 2. Build verification
# 3. Type checking
# 4. Push proceeds only if all checks pass
```

### Manual Quality Checks

```bash
# Run all quality checks manually
pnpm lint                    # ESLint
pnpm type-check             # TypeScript
pnpm test                   # All tests
pnpm test:coverage          # Coverage report
pnpm build                  # Build verification
```

### Coverage Enforcement

Our coverage system ensures code quality:

- **Changed Files**: Must have ≥70% coverage
- **Pre-commit**: Blocks commits with insufficient coverage
- **Real-time Feedback**: Shows exactly which files need more tests
- **Documentation**: Auto-updates with current coverage stats

Example enforcement:
```bash
📊 Coverage Results (minimum: 70%):
  ✅ src/features/home/<USER>
  ❌ src/features/auth/LoginForm.tsx: 45%

❌ Some files do not meet the 70% coverage requirement.
Please add tests to increase coverage before committing.
```

## 📚 Documentation

### Available Documentation

- **[TESTING.md](docs/TESTING.md)**: Comprehensive testing guide
- **API Documentation**: Auto-generated from code comments
- **Component Documentation**: Storybook (coming soon)

### Documentation Standards

- ✅ All public functions must have JSDoc comments
- ✅ Complex components need usage examples
- ✅ API endpoints documented with OpenAPI/Swagger
- ✅ README files for major features

## 🚀 Deployment

### Environments

- **Development**: `http://localhost:3000`
- **dev**: `https://dev.boma-yangu.com`
- **uat**: `https://boma-yangu.com`

### Deployment Process

#### Automatic Deployment (CI/CD)

```bash
# Push to develop → deploys to staging (uat)
git push origin dev

# Push to main → deploys to production
git push origin main
```

## 🤝 Contributing

### Getting Started

1. **Clone the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests
4. **Ensure all quality checks pass**: `pnpm test && pnpm lint && pnpm type-check`
5. **Commit your changes**: `git commit -m "feat: add amazing feature"`
6. **Push to your branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Pull Request Guidelines

- ✅ **Clear description** of changes and motivation
- ✅ **Tests included** for new functionality
- ✅ **Documentation updated** if needed
- ✅ **All CI checks passing**
- ✅ **Code review** from at least one team member

### Code Review Process

1. **Automated Checks**: CI/CD pipeline runs all tests and quality checks
2. **Peer Review**: Team member reviews code for logic and best practices
3. **Testing**: Reviewer tests functionality in staging environment
4. **Approval**: Code is approved and merged to develop/main

## 📞 Support

### Team Contacts

- **Engineers**: Patsheba Gikunda (<EMAIL>), Leroy Ombiji (<EMAIL>), Zaccheus Mwangi (<EMAIL>)
- **Project Manager**: [Contact Information]

### Getting Help

- **Questions**: Reach out to the team via teams or email
- **Documentation**: Check [docs/](docs/) directory for detailed guides

---

**Built with ❤️ by the DTB Frontend Team**
