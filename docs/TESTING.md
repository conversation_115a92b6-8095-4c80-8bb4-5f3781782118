# Testing Guide

This document provides comprehensive information about the testing setup and practices for the Boma Yangu Client application.

## Overview

Our testing setup uses:
- **Vitest** - Fast unit test runner with native TypeScript support
- **React Testing Library** - Simple and complete testing utilities for React components
- **Jest DOM** - Custom Jest matchers for DOM elements
- **JSDOM** - DOM implementation for Node.js (test environment)

## Test Structure

```
src/
├── test-utils/                 # Testing utilities and setup
│   ├── setup.ts               # Global test setup and mocks
│   ├── test-utils.tsx         # Custom render functions and providers
│   ├── mocks.ts               # Mock implementations
│   └── __tests__/             # Tests for test utilities
├── features/                  # Feature-specific tests
│   ├── home/__tests__/
│   └── onboarding/__tests__/
├── app/__tests__/             # App-level component tests
└── store/__tests__/           # Redux store tests
```

## Running Tests

### Basic Commands

```bash
# Run all tests once
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run tests with UI
pnpm test:ui

# Run tests with coverage and UI
pnpm test:coverage:ui
```

### Specialized Commands

```bash
# Run only unit tests
pnpm test:unit

# Run only test utility tests
pnpm test:utils

# Run tests for CI (with JUnit output)
pnpm test:ci

# Type checking
pnpm type-check

# View coverage report in browser
pnpm test:view-report
```

## Writing Tests

### Component Testing

```typescript
import { describe, it, expect } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders } from '@/test-utils/test-utils'
import MyComponent from '../MyComponent'

describe('MyComponent', () => {
  it('renders correctly', () => {
    renderWithProviders(<MyComponent />)
    
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })

  it('handles user interactions', async () => {
    const user = userEvent.setup()
    renderWithProviders(<MyComponent />)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    await user.click(button)
    
    expect(screen.getByText('Button clicked')).toBeInTheDocument()
  })
})
```

### Redux Testing

```typescript
import { describe, it, expect } from 'vitest'
import { renderWithProviders, mockAuthState } from '@/test-utils/test-utils'
import ConnectedComponent from '../ConnectedComponent'

describe('ConnectedComponent', () => {
  it('displays user information when authenticated', () => {
    renderWithProviders(<ConnectedComponent />, {
      initialState: mockAuthState
    })
    
    expect(screen.getByText('Test User')).toBeInTheDocument()
  })
})
```

### Custom Store Testing

```typescript
import { createTestStore } from '@/test-utils/test-utils'

describe('Custom Store Tests', () => {
  it('handles custom state', () => {
    const customState = {
      auth: { isAuthenticated: false }
    }
    const store = createTestStore(customState)
    
    renderWithProviders(<Component />, { store })
    
    // Test with custom store state
  })
})
```

## Test Utilities

### renderWithProviders

Custom render function that wraps components with necessary providers:
- Redux Provider with test store
- Material-UI ThemeProvider
- Emotion cache provider

```typescript
renderWithProviders(<Component />, {
  initialState: customState,  // Optional custom Redux state
  store: customStore,         // Optional custom store instance
  // ...other render options
})
```

### Mock Data

Pre-configured mock data for common scenarios:

```typescript
import { mockUser, mockAuthState } from '@/test-utils/test-utils'

// Mock user object
const user = mockUser // { id: '1', name: 'Test User', email: '<EMAIL>' }

// Mock authenticated state
const state = mockAuthState // Complete state with authenticated user
```

### Mocks

Comprehensive mocks for external dependencies:
- Next.js router and navigation
- @dtbx/ui components
- @dtbx/store hooks and actions
- Browser APIs (matchMedia, IntersectionObserver, etc.)

## Coverage Requirements

We maintain high code coverage standards:
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

Coverage reports are generated in the `coverage/` directory and can be viewed in the browser.

## Best Practices

### 1. Test Behavior, Not Implementation

```typescript
// ✅ Good - tests behavior
expect(screen.getByText('Welcome')).toBeInTheDocument()

// ❌ Bad - tests implementation
expect(component.state.showWelcome).toBe(true)
```

### 2. Use Semantic Queries

```typescript
// ✅ Good - semantic queries
screen.getByRole('button', { name: 'Submit' })
screen.getByLabelText('Email address')

// ❌ Bad - implementation details
screen.getByTestId('submit-btn')
```

### 3. Test User Interactions

```typescript
// ✅ Good - realistic user interactions
const user = userEvent.setup()
await user.click(screen.getByRole('button'))
await user.type(screen.getByLabelText('Email'), '<EMAIL>')
```

### 4. Keep Tests Focused

Each test should verify one specific behavior or outcome.

### 5. Use Descriptive Test Names

```typescript
// ✅ Good
it('displays error message when email is invalid')

// ❌ Bad
it('handles email validation')
```

## Debugging Tests

### Running Specific Tests

```bash
# Run tests matching a pattern
pnpm test HomePage

# Run tests in a specific file
pnpm test src/features/home/<USER>/HomePage.test.tsx

# Run tests with debug output
pnpm test --reporter=verbose
```

### Using the Test UI

The Vitest UI provides an interactive way to run and debug tests:

```bash
pnpm test:ui
```

This opens a web interface where you can:
- Run individual tests
- View test results and coverage
- Debug failing tests
- Explore test files

## Continuous Integration

Tests run automatically on:
- Push to main/develop branches
- Pull requests
- Multiple Node.js versions (18.x, 20.x)

The CI pipeline includes:
1. Dependency installation
2. Linting
3. Type checking
4. Unit tests with coverage
5. Build verification

## Troubleshooting

### Common Issues

1. **Mock not working**: Ensure mocks are set up before imports
2. **Provider errors**: Use `renderWithProviders` instead of `render`
3. **Async issues**: Use `await` with user interactions and `findBy*` queries
4. **Coverage issues**: Check if files are excluded in vitest config

### Getting Help

- Check the [Vitest documentation](https://vitest.dev/)
- Review [React Testing Library guides](https://testing-library.com/docs/react-testing-library/intro/)
- Look at existing tests for examples
- Ask the team for guidance on complex scenarios
