{"/Users/<USER>/projects/work/boma-yangu-client/src/app/Error.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/Error.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}}, "fnMap": {"0": {"name": "Error", "decl": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 30}}, "loc": {"start": {"line": 11, "column": 3}, "end": {"line": 13, "column": null}}}}, "branchMap": {}, "s": {"0": 7}, "f": {"0": 7}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/app/sidebar.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/sidebar.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 51}, "end": {"line": 21, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1}, "f": {}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/app/home/<USER>": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/home/<USER>", "statementMap": {"0": {"start": {"line": 3, "column": 13}, "end": {"line": 6, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 19}}, "loc": {"start": {"line": 3, "column": 19}, "end": {"line": 6, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 3}, "f": {"0": 3}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/app/onboarding/page.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/onboarding/page.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 19}, "end": {"line": 5, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": 25}}, "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 5, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 4}, "f": {"0": 4}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/features/home/<USER>": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/features/home/<USER>", "statementMap": {"0": {"start": {"line": 3, "column": 17}, "end": {"line": 22, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 20, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 17}, "end": {"line": 3, "column": 23}}, "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 22, "column": null}}}}, "branchMap": {}, "s": {"0": 2, "1": 9}, "f": {"0": 9}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/features/onboarding/OnboardingPage.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/features/onboarding/OnboardingPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 23}, "end": {"line": 22, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 20, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 29}}, "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 22, "column": null}}}}, "branchMap": {}, "s": {"0": 2, "1": 12}, "f": {"0": 12}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/store/AppProvider.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/store/AppProvider.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": null}}, "1": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}}, "fnMap": {"0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 36}}, "loc": {"start": {"line": 15, "column": 3}, "end": {"line": 17, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 6}, "f": {"0": 6}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/store/index.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/store/index.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": null}}, "1": {"start": {"line": 10, "column": 14}, "end": {"line": 18, "column": null}}, "2": {"start": {"line": 13, "column": 4}, "end": {"line": 17, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 15}}, "loc": {"start": {"line": 13, "column": 4}, "end": {"line": 17, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1}, "f": {"0": 1}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/store/reducers/store.ts": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/store/reducers/store.ts", "statementMap": {"0": {"start": {"line": 9, "column": 27}, "end": {"line": 14, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 3}, "f": {}, "b": {}}}